"""
Wake Word + Real-time Streaming Auto-typing example for RealtimeSTT

This example demonstrates wake word activation combined with real-time streaming auto-typing functionality.
The application will:
1. Listen continuously for a wake word (like "jarvis")
2. When the wake word is detected, start recording your speech
3. Transcribe the speech to text using the local Whisper model with REAL-TIME STREAMING
4. Automatically type the transcribed text AS YOU SPEAK into whatever text field is currently active/focused

Features:
- Wake word activation (no constant recording)
- Real-time streaming transcription (text appears as you speak, like live captions)
- Smooth word-based streaming with minimal backspacing/corrections
- Append-only approach - new words are added without disrupting previous text
- Local processing only (no API tokens consumed)
- Auto-typing into any active text input field with live updates
- Conservative correction strategy (only last 2-3 words corrected if needed)
- Visual feedback and status updates

Usage:
1. Run this script
2. Open any text editor, word processor, or text input field
3. Click in the text field to give it focus
4. Say the wake word "jarvis" (or your chosen wake word)
5. Speak your message
6. Watch as your speech is automatically typed in real-time as you speak
7. The system will automatically correct and refine the text as the transcription stabilizes

Press Ctrl+C to exit.
"""

import os
import sys
import time
import threading

# Handle Windows-specific torch audio initialization
if os.name == "nt" and (3, 8) <= sys.version_info < (3, 99):
    try:
        from torchaudio._extension.utils import _init_dll_path
        _init_dll_path()
    except ImportError:
        pass

from RealtimeSTT import AudioToTextRecorder

# Audio feedback functionality
try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    pygame = None


class AudioFeedback:
    """Handles audio feedback for wake word detection and listening states"""

    def __init__(self, enable_audio=True, sounds_dir="sounds"):
        self.enable_audio = enable_audio and PYGAME_AVAILABLE
        self.sounds_dir = sounds_dir
        self.sounds = {}

        if self.enable_audio:
            try:
                # Initialize pygame mixer for audio playback
                pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
                pygame.mixer.init()
                self._load_sounds()
            except Exception as e:
                print(f"Warning: Could not initialize audio feedback: {e}")
                self.enable_audio = False

    def _load_sounds(self):
        """Load audio files for different states - using only listening_start.wav for all triggers"""
        # Use only listening_start.wav for all audio feedback
        listening_start_path = os.path.join(self.sounds_dir, 'listening_start.wav')

        if os.path.exists(listening_start_path):
            try:
                # Load the same sound for all triggers
                sound = pygame.mixer.Sound(listening_start_path)
                sound.set_volume(0.7)  # Set volume to a reasonable level (0.0 to 1.0)

                # Use the same sound for all events
                self.sounds['wake_detected'] = sound      # Sound when wake word is detected
                self.sounds['listening_start'] = sound    # Sound when listening starts
                self.sounds['listening_stop'] = sound     # Sound when listening stops
            except Exception as e:
                print(f"Warning: Could not load sound {listening_start_path}: {e}")
        else:
            print(f"Warning: Sound file not found: {listening_start_path}")

    def play_sound(self, sound_name):
        """Play a specific sound in a separate thread to avoid blocking"""
        if self.enable_audio and sound_name in self.sounds:
            def play_async():
                try:
                    self.sounds[sound_name].play()
                except Exception as e:
                    print(f"Warning: Could not play sound {sound_name}: {e}")

            # Play sound in a separate thread to avoid blocking the main process
            threading.Thread(target=play_async, daemon=True).start()


class WakeWordAutoTyper:
    def __init__(self, enable_audio_feedback=True, sounds_dir="sounds"):
        self.wake_word_detected = False
        self.typing_active = False

        # Add safeguards against duplicate processing
        self.last_processed_text = ""
        self.last_process_time = 0
        self.processing_lock = threading.Lock()

        # Visual feedback state
        self.listening_indicator = "Listening..."
        self.indicator_typed = False
        self.indicator_length = 0

        # Initialize audio feedback system
        self.audio_feedback = AudioFeedback(enable_audio_feedback, sounds_dir)
    
    def on_wakeword_detected(self):
        """Callback when wake word is detected"""
        self.wake_word_detected = True
        # Play wake word detection sound
        self.audio_feedback.play_sound('wake_detected')

    def on_wakeword_timeout(self):
        """Callback when wake word times out"""
        # Play listening stop sound when wake word times out
        self.audio_feedback.play_sound('listening_stop')

    def on_wakeword_detection_start(self):
        """Callback when wake word detection starts"""
        pass

    def on_wakeword_detection_end(self):
        """Callback when wake word detection ends"""
        pass

    def on_recording_start(self):
        """Callback when recording starts"""
        # Display "Listening..." indicator to show user that recording is active
        try:
            import pyautogui
            import time

            # Small delay to ensure focus is ready
            time.sleep(0.1)

            # Type the listening indicator
            pyautogui.typewrite(self.listening_indicator, interval=0.01)
            self.indicator_typed = True
            self.indicator_length = len(self.listening_indicator)

            print(f"[DEBUG] Typed listening indicator: '{self.listening_indicator}'")

        except Exception as e:
            print(f"[ERROR] Failed to type listening indicator: {e}")
            self.indicator_typed = False
            self.indicator_length = 0

        # Play listening start sound when recording begins
        self.audio_feedback.play_sound('listening_start')

    def on_recording_stop(self):
        """Callback when recording stops"""
        print(f"[DEBUG] Recording stopped, indicator_typed: {self.indicator_typed}")

        # Play listening stop sound when recording ends
        self.audio_feedback.play_sound('listening_stop')

    def on_typing_start(self):
        """Callback when auto-typing starts"""
        self.typing_active = True

    def on_typing_complete(self):
        """Callback when auto-typing completes (not used since we disabled built-in auto-typing)"""
        self.typing_active = False

    def on_typing_error(self, error):
        """Callback when auto-typing encounters an error (not used since we disabled built-in auto-typing)"""
        self.typing_active = False



    def clear_listening_indicator(self):
        """Clear the 'Listening...' indicator if it was typed"""
        if self.indicator_typed and self.indicator_length > 0:
            try:
                import pyautogui

                # Backspace the exact number of characters in the indicator
                for _ in range(self.indicator_length):
                    pyautogui.press('backspace')

                # Reset indicator state
                self.indicator_typed = False
                self.indicator_length = 0


            except Exception as e:
                print(f"[ERROR] Failed to clear listening indicator: {e}")
                # Reset state even if clearing failed to prevent future issues
                self.indicator_typed = False
                self.indicator_length = 0

    def process_text(self, text):
        """Process final transcribed text and type it directly"""
        import time

        # Use lock to prevent concurrent execution
        with self.processing_lock:
            # Debug information
            current_time = time.strftime("%H:%M:%S")

            # Always clear the listening indicator first, regardless of text content
            self.clear_listening_indicator()

            if not text or not text.strip():
                return

            cleaned_text = text.strip()
            current_time_seconds = time.time()

            # Check for duplicate processing
            if (cleaned_text == self.last_processed_text and
                current_time_seconds - self.last_process_time < 2.0):  # 2 second window
                return

            # Type the final text directly
            try:
                import pyautogui

                # Add small delay to ensure focus
                time.sleep(0.1)

                # Type the final text
                pyautogui.typewrite(cleaned_text, interval=0.01)

            except Exception as e:
                print(f"[ERROR] pyautogui error in process_text: {e}")
                import traceback
                traceback.print_exc()

            # Update tracking variables
            self.last_processed_text = cleaned_text
            self.last_process_time = current_time_seconds
    
    def run(self):
        print("Ready - Say 'jarvis' then speak your message")
        print()
        
        try:
            # Initialize the recorder with wake word + real-time auto-typing
            recorder = AudioToTextRecorder(
                # Model configuration
                model="small",  # Upgraded model for better transcription accuracy
                language="en",  # Set to your preferred language

                # Real-time transcription settings - DISABLED FOR FINAL TEXT MODE
                enable_realtime_transcription=False,  # Disable streaming transcription
                realtime_model_type="tiny",  # Fast model for real-time processing
                realtime_processing_pause=0.02,  # Reduced from 0.05s to 0.02s for faster processing
                on_realtime_transcription_update=None,  # Disable streaming callback
                on_realtime_transcription_stabilized=None,  # Disable stabilized callback

                # Wake word configuration - OPTIMIZED FOR LOW LATENCY
                wakeword_backend="pvporcupine",  # Use Porcupine backend
                wake_words="jarvis",  # Using "jarvis" as closest alternative to "boss"
                wake_words_sensitivity=0.6,  # Adjust sensitivity (0.0-1.0)
                wake_word_timeout=3,  # Reduced from 5s to 3s for faster timeout
                wake_word_activation_delay=0,  # Immediate activation

                # Wake word callbacks
                on_wakeword_detected=self.on_wakeword_detected,
                on_wakeword_timeout=self.on_wakeword_timeout,
                on_wakeword_detection_start=self.on_wakeword_detection_start,
                on_wakeword_detection_end=self.on_wakeword_detection_end,

                # Auto-typing configuration - COMPLETELY DISABLED
                enable_auto_typing=False,  # Disable built-in auto-typing completely
                auto_typing_delay=0.01,
                auto_typing_fail_safe=True,
                auto_typing_add_space=False,  # We handle spacing manually

                # Auto-typing callbacks - REMOVED to prevent interference
                on_auto_typing_start=None,  # Remove callback
                on_auto_typing_complete=None,  # Remove callback
                on_auto_typing_error=None,  # Remove callback

                # Recording callbacks
                on_recording_start=self.on_recording_start,
                on_recording_stop=self.on_recording_stop,

                # Voice activity detection settings - BALANCED FOR NATURAL SPEECH
                silero_sensitivity=0.4,  # Balanced sensitivity to avoid cutting off during thinking pauses
                silero_deactivity_detection=True,  # Use Silero for more robust end-of-speech detection
                webrtc_sensitivity=3,  # Standard sensitivity for reliable voice detection
                post_speech_silence_duration=0.9,  # Increased to 0.9s to allow for natural thinking pauses
                min_length_of_recording=0.4,  # Slightly increased to ensure complete words are captured

                # Audio quality settings - OPTIMIZED FOR LOW LATENCY
                beam_size=3,  # Reduced from 5 to 3 for faster transcription with minimal accuracy loss
                beam_size_realtime=1,  # Reduced from 3 to 1 for fastest real-time processing
                faster_whisper_vad_filter=True,  # Keep VAD filtering for cleaner audio
                normalize_audio=True,  # Keep audio normalization for consistency

                # UI settings
                spinner=False,  # Disable spinner for clean output

                # Logging settings
                no_log_file=True,  # Disable log file creation to eliminate unnecessary file I/O

                # Early transcription for reduced latency - BALANCED SETTING
                early_transcription_on_silence=4,  # Increased from 2 to 4 to avoid premature transcription during speech pauses
            )

            # CRITICAL: Explicitly disable the auto_typer to prevent any built-in auto-typing
            recorder.auto_typer = None
            recorder.enable_auto_typing = False

            # Additional safety check for any auto-typing mechanisms
            if hasattr(recorder, '_on_realtime_transcription_stabilized'):
                original_stabilized = recorder._on_realtime_transcription_stabilized
                def safe_stabilized_callback(text):
                    # Call original callback but without auto-typing
                    if recorder.on_realtime_transcription_stabilized:
                        recorder.on_realtime_transcription_stabilized(text)
                recorder._on_realtime_transcription_stabilized = safe_stabilized_callback

            # Add debug info indicating the app is ready and initialized

            # Main loop - continuously listen for wake word and process speech
            while True:
                try:
                    import time

                    # This will wait for wake word, then record and get final transcription
                    start_time = time.time()
                    text = recorder.text()  # Get final text only
                    end_time = time.time()

                    if text and text.strip():
                        self.process_text(text)
                    else:
                        self.clear_listening_indicator()

                except KeyboardInterrupt:
                    print("\nStopping...")
                    break
                except Exception as e:
                    print(f"Error: {e}")
                    time.sleep(1)
                    
        except ImportError as e:
            if "pyautogui" in str(e):
                print("Error: pyautogui is required for auto-typing functionality.")
                print("Install it with: pip install pyautogui")
            elif "pvporcupine" in str(e):
                print("Error: pvporcupine is required for wake word detection.")
                print("Install it with: pip install pvporcupine")
            else:
                print(f"Import error: {e}")
            sys.exit(1)

        except Exception as e:
            print(f"Failed to initialize recorder: {e}")
            sys.exit(1)

        finally:
            try:
                recorder.shutdown()
            except:
                pass


def main():
    typer = WakeWordAutoTyper()
    typer.run()


if __name__ == "__main__":
    main()
