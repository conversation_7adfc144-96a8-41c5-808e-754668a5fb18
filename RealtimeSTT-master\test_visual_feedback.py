#!/usr/bin/env python3
"""
Test script to verify the visual feedback functionality
"""

import sys
import os
import time

# Add the RealtimeSTT directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from wake_word_auto_typing import Wake<PERSON>ordAutoTyper

def test_visual_feedback_state():
    """Test that visual feedback state variables are properly initialized"""
    print("Testing visual feedback state initialization...")
    
    typer = WakeWordAutoTyper()
    
    # Check that visual feedback attributes exist
    required_attrs = ['listening_indicator', 'indicator_typed', 'indicator_length']
    missing_attrs = []
    
    for attr in required_attrs:
        if not hasattr(typer, attr):
            missing_attrs.append(attr)
    
    if missing_attrs:
        print(f"✗ Missing visual feedback attributes: {missing_attrs}")
        return False
    
    # Check initial values
    if typer.listening_indicator != "Listening...":
        print(f"✗ Wrong listening indicator: '{typer.listening_indicator}' (expected 'Listening...')")
        return False
    
    if typer.indicator_typed != False:
        print(f"✗ Wrong initial indicator_typed: {typer.indicator_typed} (expected False)")
        return False
    
    if typer.indicator_length != 0:
        print(f"✗ Wrong initial indicator_length: {typer.indicator_length} (expected 0)")
        return False
    
    print("✓ Visual feedback state properly initialized")
    return True

def test_recording_start_callback():
    """Test that recording start callback sets up visual feedback"""
    print("\nTesting recording start callback...")
    
    typer = WakeWordAutoTyper()
    
    # Capture the debug output
    import io
    from contextlib import redirect_stdout
    
    captured_output = io.StringIO()
    
    try:
        with redirect_stdout(captured_output):
            typer.on_recording_start()
    except Exception as e:
        print(f"Exception during on_recording_start: {e}")
        return False
    
    output = captured_output.getvalue()
    print("Debug output from on_recording_start:")
    print(output)
    
    # Check if the indicator was typed
    if "Typed listening indicator: 'Listening...'" in output:
        print("✓ Listening indicator was typed successfully")
        
        # Check state variables
        if typer.indicator_typed and typer.indicator_length == len("Listening..."):
            print("✓ Visual feedback state updated correctly")
            return True
        else:
            print(f"✗ State not updated: indicator_typed={typer.indicator_typed}, length={typer.indicator_length}")
            return False
    else:
        print("✗ Listening indicator was not typed")
        return False

def test_clear_indicator():
    """Test that the clear indicator function works correctly"""
    print("\nTesting clear indicator functionality...")
    
    typer = WakeWordAutoTyper()
    
    # Simulate that indicator was typed
    typer.indicator_typed = True
    typer.indicator_length = len("Listening...")
    
    # Capture the debug output
    import io
    from contextlib import redirect_stdout
    
    captured_output = io.StringIO()
    
    try:
        with redirect_stdout(captured_output):
            typer.clear_listening_indicator()
    except Exception as e:
        print(f"Exception during clear_listening_indicator: {e}")
        return False
    
    output = captured_output.getvalue()
    print("Debug output from clear_listening_indicator:")
    print(output)
    
    # Check if the indicator was cleared
    if "Clearing listening indicator" in output and "Listening indicator cleared successfully" in output:
        print("✓ Listening indicator cleared successfully")
        
        # Check state variables were reset
        if not typer.indicator_typed and typer.indicator_length == 0:
            print("✓ Visual feedback state reset correctly")
            return True
        else:
            print(f"✗ State not reset: indicator_typed={typer.indicator_typed}, length={typer.indicator_length}")
            return False
    else:
        print("✗ Listening indicator was not cleared properly")
        return False

def test_process_text_with_indicator():
    """Test that process_text clears indicator before typing final text"""
    print("\nTesting process_text with indicator clearing...")
    
    typer = WakeWordAutoTyper()
    
    # Simulate that indicator was typed
    typer.indicator_typed = True
    typer.indicator_length = len("Listening...")
    
    test_text = "Hello world test"
    
    # Capture the debug output
    import io
    from contextlib import redirect_stdout
    
    captured_output = io.StringIO()
    
    try:
        with redirect_stdout(captured_output):
            typer.process_text(test_text)
    except Exception as e:
        print(f"Exception during process_text: {e}")
        return False
    
    output = captured_output.getvalue()
    print("Debug output from process_text:")
    print(output)
    
    # Check if indicator was cleared and text was typed
    if ("Clearing listening indicator" in output and 
        "Listening indicator cleared successfully" in output and
        f"Typing final text: '{test_text}'" in output):
        print("✓ Indicator cleared and final text typed successfully")
        return True
    else:
        print("✗ Process text did not work correctly")
        return False

def test_empty_text_handling():
    """Test that empty text still clears the indicator"""
    print("\nTesting empty text handling...")
    
    typer = WakeWordAutoTyper()
    
    # Simulate that indicator was typed
    typer.indicator_typed = True
    typer.indicator_length = len("Listening...")
    
    # Capture the debug output
    import io
    from contextlib import redirect_stdout
    
    captured_output = io.StringIO()
    
    try:
        with redirect_stdout(captured_output):
            typer.process_text("")  # Empty text
    except Exception as e:
        print(f"Exception during process_text with empty text: {e}")
        return False
    
    output = captured_output.getvalue()
    print("Debug output from process_text with empty text:")
    print(output)
    
    # Check if indicator was cleared even with empty text
    if ("Clearing listening indicator" in output and 
        "Skipping empty text in process_text (indicator cleared)" in output):
        print("✓ Indicator cleared correctly for empty text")
        return True
    else:
        print("✗ Empty text handling did not work correctly")
        return False

def main():
    """Run all tests"""
    print("Testing visual feedback functionality...\n")
    
    test1_passed = test_visual_feedback_state()
    test2_passed = test_recording_start_callback()
    test3_passed = test_clear_indicator()
    test4_passed = test_process_text_with_indicator()
    test5_passed = test_empty_text_handling()
    
    print(f"\n{'='*50}")
    print("TEST RESULTS:")
    print(f"Visual feedback state: {'PASS' if test1_passed else 'FAIL'}")
    print(f"Recording start callback: {'PASS' if test2_passed else 'FAIL'}")
    print(f"Clear indicator: {'PASS' if test3_passed else 'FAIL'}")
    print(f"Process text with indicator: {'PASS' if test4_passed else 'FAIL'}")
    print(f"Empty text handling: {'PASS' if test5_passed else 'FAIL'}")
    
    all_passed = all([test1_passed, test2_passed, test3_passed, test4_passed, test5_passed])
    
    if all_passed:
        print("\n✓ All tests PASSED! Visual feedback functionality is working correctly.")
        print("The system will now show 'Listening...' during recording and replace it with transcription.")
        return 0
    else:
        print("\n✗ Some tests FAILED. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
